from flask import Flask
from flask_socketio import So<PERSON><PERSON>
from flask_cors import CORS
from app.routers.streaming_avatar import streaming_avatar, register_socketio_handlers
from app.routers.chatbot import chatbot

# Import emailSend with error handling
try:
    from app.routers.emailSend import emailSend
    print("emailSend imported successfully!")
except Exception as e:
    print(f"Error importing emailSend: {e}")
    import traceback
    traceback.print_exc()
    emailSend = None

# Create Flask app
app = Flask(__name__)

# --- Enable CORS for REST API ---
CORS(app, resources={r"/api/*": {"origins": "*"}})  

# Register blueprints
app.register_blueprint(streaming_avatar, url_prefix="/api/streaming-avatar")
app.register_blueprint(chatbot, url_prefix="/api/chatbot")

# Register emailSend blueprint if imported successfully
if emailSend is not None:
    app.register_blueprint(emailSend, url_prefix="/api/sendEmail")
    print("emailSend blueprint registered successfully!")
else:
    print("emailSend blueprint not registered due to import error")

# Print all routes for debugging
print("\nRegistered routes:")
for rule in app.url_map.iter_rules():
    print(f"  {rule.rule} -> {rule.endpoint} [{', '.join(rule.methods)}]")

# Initialize SocketIO with eventlet
socketio = SocketIO(app, cors_allowed_origins="*", async_mode="eventlet")

# Register websocket event handlers
register_socketio_handlers(socketio)

if __name__ == "__main__":
    # Run
    socketio.run(app, host="0.0.0.0", port=5001, debug=True)