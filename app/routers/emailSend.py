from flask import Flask, request, jsonify, send_file, Blueprint, make_response
import requests
import json
import os
import resend
import re
import dotenv
from xhtml2pdf import pisa
from datetime import datetime
import base64
from io import BytesIO
import requests

emailSend = Blueprint("emailSend", __name__)

@emailSend.route('/a', methods=['POST', 'OPTIONS'])
def send_email():
    if request.method == 'OPTIONS':
        # Handle preflight CORS request
        response = make_response('', 200)
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

    try:
        data = request.json
        student_email = data.get('student_email')
        teacher_email = data.get('teacher_email')
        report_md = data.get('report_md')
        report_history = data.get('report_history')

        # Input validation
        if not student_email:
            return jsonify({'success': False, 'error': 'Student email is required'}), 400

        if not teacher_email:
            return jsonify({'success': False, 'error': 'Teacher email is required'}), 400

        if not report_md or not report_history:
            return jsonify({'success': False, 'error': 'Conversation is required'}), 400

        # Validate email format (basic validation)
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, student_email):
            return jsonify({'success': False, 'error': 'Invalid student email format'}), 400

        if not re.match(email_pattern, teacher_email):
            return jsonify({'success': False, 'error': 'Invalid teacher email format'}), 400


        # Check API key
        Resend_API_KEY = os.getenv('RESEND_API_KEY')
        if not Resend_API_KEY:
            return jsonify({'success': False, 'error': 'Resend API key not configured'}), 500

        html_content = report_history_to_html(report_history)
        resend.api_key = Resend_API_KEY
        params: resend.Emails.SendParams = {
            "from": "<EMAIL>",
            "to": [student_email],
            "cc": [teacher_email],
            "attachments": [
                {
                    "filename": "report.pdf",
                    "content": html_to_pdf(html_content)
                },
                {
                    "filename": "report.md",
                    "content": report_md_to_md(report_md)
                }
            ],
            "subject": "HKBU Chatbot Report",
            "html": create_email(html_content)
        }
        email = resend.Emails.send(params)
        return jsonify({'success': True, 'message': 'Email sent successfully!'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400


def create_email(html_content):
    email_template = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>HKBU Chatbot Report</title>
    <style>
        /* Reset default styles for email client consistency */
        body {{
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            color: #333333;
            background-color: #f4f4f9;
            line-height: 1.6;
        }}
        * {{
            box-sizing: border-box;
        }}
        /* Container */
        .container {{
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
        }}
        /* Header */
        .header {{
            background-color: #003087;
            padding: 20px;
            text-align: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }}
        .header img {{
            max-width: 120px;
            height: auto;
            display: block;
            margin: 0 auto;
        }}
        .header h1 {{
            color: #ffffff;
            font-size: 24px;
            margin: 10px 0 0;
            font-weight: 600;
        }}
        /* Content */
        .content {{
            padding: 25px;
        }}
        .content p {{
            margin: 0 0 15px;
            font-size: 16px;
            color: #333333;
        }}
        .content h2 {{
            font-size: 20px;
            color: #003087;
            margin: 20px 0 10px;
        }}
        .content h3 {{
            font-size: 18px;
            color: #003087;
            margin: 15px 0 8px;
        }}
        .content ul, .content ol {{
            margin: 0 0 15px 20px;
            padding: 0;
        }}
        .content li {{
            margin-bottom: 8px;
            font-size: 16px;
        }}
        /* Button */
        .button {{
            display: inline-block;
            padding: 12px 24px;
            background-color: #003087;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
            transition: background-color 0.3s;
        }}
        .button:hover {{
            background-color: #00205b;
        }}
        /* Footer */
        .footer {{
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666666;
            background-color: #f4f4f9;
            border-top: 1px solid #dcdcdc;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }}
        .footer a {{
            color: #003087;
            text-decoration: underline;
        }}
        /* Responsive Design */
        @media only screen and (max-width: 600px) {{
            .container {{
                width: 100% !important;
                border: none;
            }}
            .content {{
                padding: 15px;
            }}
            .header h1 {{
                font-size: 20px;
            }}
            .button {{
                width: 100%;
                box-sizing: border-box;
            }}
        }}
    </style>
</head>
<body>
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background-color: #f4f4f9;">
        <tr>
            <td align="center">
                <table role="presentation" class="container" width="100%" style="max-width: 600px;" cellspacing="0" cellpadding="0">
                    <tr>
                        <td class="header">
                            <h1>Chatbot Report</h1>
                        </td>
                    </tr>
                    <tr>
                        <td class="content">
                            <p>Dear Student,</p>
                            <p>Your learning session report is ready. Please find the attached files or view the details below.</p>
                            <hr>
                            {html_content}
                            <hr>
                            <p>Thank you for using the HKBU Chatbot. For any questions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
                        </td>
                    </tr>
                    <tr>
                        <td class="footer">
                            <p>
                                Created by: Dr. Simon Wang, Innovation Officer<br>
                                Language Centre, Hong Kong Baptist University<br>
                                &copy; 2025 HKBU Chatbot
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    """
    return email_template


def report_history_to_html(report_history):
    """Convert report history to PDF and return base64 encoded content"""
    try:
        if not report_history or len(report_history) == 0:
            raise Exception("No conversation to export")

        # Calculate duration and metadata
        now = datetime.now()
        duration = 0
        if len(report_history) > 0:
            first_timestamp = report_history[0].get('timestamp', '')
            last_timestamp = report_history[-1].get('timestamp', '')

            try:
                if isinstance(first_timestamp, str) and isinstance(last_timestamp, str):
                    # Handle ISO 8601 format
                    first_dt = datetime.fromisoformat(first_timestamp.replace('Z', '+00:00'))
                    last_dt = datetime.fromisoformat(last_timestamp.replace('Z', '+00:00'))
                    duration = round((last_dt - first_dt).total_seconds() / 60)
                elif isinstance(first_timestamp, (int, float)) and isinstance(last_timestamp, (int, float)):
                    # Handle numeric timestamps (milliseconds)
                    duration = round((last_timestamp - first_timestamp) / 1000 / 60)
            except (ValueError, TypeError):
                duration = 0

        # Generate summary
        summary = generate_summary(report_history)

        # Create HTML content for PDF
        html_content = f"""
        <h1 style="text-align: center; color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
            HKBU Learning Session Report
        </h1>

        <div style="margin: 20px 0;">
            <p><strong>Generated:</strong> {now.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p><strong>Duration:</strong> {duration} minutes</p>
            <p><strong>Total Messages:</strong> {len(report_history)}</p>
        </div>

        <h2 style="color: #34495e; margin-top: 30px;">Session Summary</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <p>{summary}</p>
        </div>

        <h2 style="color: #34495e; margin-top: 30px;">Complete Conversation</h2>
        """

        # Add conversation messages
        for msg in report_history:
            role = "You:" if msg.get('role') == 'user' else "Assistant:"
            content = msg.get('content', '').replace('<', '&lt;').replace('>', '&gt;')
            timestamp = msg.get('timestamp', '')

            # Convert timestamp to readable format
            time_str = "Unknown time"
            if timestamp:
                try:
                    if isinstance(timestamp, str):
                        # Handle ISO 8601 format like "2025-09-03T02:21:54.582Z"
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime('%H:%M:%S')
                    elif isinstance(timestamp, (int, float)) and timestamp > 0:
                        # Handle numeric timestamp (milliseconds)
                        time_str = datetime.fromtimestamp(timestamp / 1000).strftime('%H:%M:%S')
                except (ValueError, TypeError):
                    time_str = "Unknown time"

            html_content += f"""
            <div style="margin: 15px 0; padding: 10px; border-left: 3px solid #3498db;">
                <p style="margin: 0 0 5px 0;"><strong>{role}</strong></p>
                <p style="margin: 0 0 5px 0; line-height: 1.6;">{content}</p>
                <p style="margin: 0; font-size: 12px; color: #666;">{time_str}</p>
            </div>
            """

        # Add footer
        html_content += """
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #666;">
            <p>Created by: Dr. Simon Wang, Innovation Officer</p>
            <p>Language Centre, Hong Kong Baptist University</p>
            <p><EMAIL></p>
        </div>
        """

        # Convert to PDF using existing html_to_pdf function
        return html_content

    except Exception as e:
        raise Exception(f"Error generating PDF from history: {str(e)}")

def generate_summary(report_history):
    """Generate a summary of the conversation"""
    if not report_history or len(report_history) == 0:
        return "No conversation to summarize."

    user_messages = [msg for msg in report_history if msg.get('role') == 'user']
    assistant_messages = [msg for msg in report_history if msg.get('role') == 'assistant']

    summary = f"This learning session consisted of {len(user_messages)} student questions and {len(assistant_messages)} assistant responses. "

    if len(user_messages) > 0:
        # Get first and last user messages for context
        first_msg = user_messages[0].get('content', '')[:100]
        if len(user_messages) > 1:
            last_msg = user_messages[-1].get('content', '')[:100]
            summary += f"The conversation started with: '{first_msg}...' and concluded with: '{last_msg}...'"
        else:
            summary += f"The student asked: '{first_msg}...'"

    return summary

def report_md_to_md(report_md):
    """Convert markdown report to base64 encoded content for email attachment"""
    try:
        if not report_md:
            raise Exception("No markdown content provided")

        # Encode the markdown content as base64
        md_bytes = report_md.encode('utf-8')
        base64_content = base64.b64encode(md_bytes).decode('utf-8')
        return base64_content

    except Exception as e:
        raise Exception(f"Error processing markdown report: {str(e)}")

def html_to_pdf(html_content):
    """Convert HTML to PDF and return base64 encoded content"""
    try:
        # Create a complete HTML document with proper structure
        complete_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 20px;
                    padding: 0;
                }}
                h1, h2, h3 {{
                    color: #2c3e50;
                    margin-top: 20px;
                    margin-bottom: 10px;
                }}
                h1 {{
                    font-size: 24px;
                    text-align: center;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                h2 {{
                    font-size: 20px;
                    color: #34495e;
                }}
                h3 {{
                    font-size: 16px;
                    color: #7f8c8d;
                }}
                p {{
                    margin-bottom: 10px;
                }}
                div {{
                    margin-bottom: 10px;
                }}
                strong {{
                    font-weight: bold;
                }}
                .footer {{
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        # Create a BytesIO buffer for the PDF
        result = BytesIO()

        # Convert HTML to PDF using xhtml2pdf
        pdf = pisa.pisaDocument(BytesIO(complete_html.encode("utf-8")), result)

        if not pdf.err:
            # Get PDF content and encode as base64
            pdf_content = result.getvalue()
            result.close()
            base64_content = base64.b64encode(pdf_content).decode('utf-8')
            return base64_content
        else:
            result.close()
            raise Exception("Error generating PDF")

    except Exception as e:
        raise Exception(f"Error converting HTML to PDF: {str(e)}")
